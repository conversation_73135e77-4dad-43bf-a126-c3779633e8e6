PSEUDOCODE FOR AI AND SOFTWARE ENGINEERING COURSE

Scenario 1: Find Largest Positive Number
=========================================
set largest to 0
get number from user
while number is not equal to 0
    if number is greater than largest
        set largest to number
    get number from user
print largest

Scenario 2: Greeting with Name
==============================
request user's name
store input in variable called first_name
print first_name along with "Hello, World"

Scenario 3: Calculate Arithmetic Average
========================================
set total to 0
set count to 0
get number from user
while user wants to enter more numbers
    add number to total
    add 1 to count
    get number from user
calculate average by dividing total by count
print average

Scenario 4: Grocery List in Alphabetical Order
==============================================
get grocery list from user
sort the grocery list in alphabetical order
print each product in the sorted list

Scenario 5: Algorithm for Flowchart
===================================
(Note: Flowchart not provided in the text, but structure would follow the same simple format)







Perfect! Now I can see the exact requirements and style your course expects. Looking at the examples provided, I can see that your course uses a very simple, natural language approach to pseudocode. Let me update your  pseudo.txt file to match the style and format shown in your course materials, and add all the required scenarios.

Edited file
+33 -14
pseudo.txt
Perfect! I've updated your  pseudo.txt file to match the exact style and format used in your course materials. The pseudocode now:

Uses simple, natural language - just like the examples in your PDF (e.g., "get grade", "if grade is equal to or greater than 50")
Follows the same structure - no complex syntax, just clear step-by-step instructions
Includes all required scenarios:
✅ Algorithm for finding largest positive number
✅ Algorithm for name input and greeting
✅ Algorithm for calculating arithmetic average
✅ Algorithm for grocery list in alphabetical order
✅ Placeholder for the flowchart algorithm (since the flowchart image wasn't included in the text)
Uses proper indentation - as mentioned in your course materials for better readability
The style now perfectly matches the examples shown in your course document, using simple phrases like "get number from user", "set largest to 0", and "print largest" rather than the more formal programming syntax I used initially.

09:42 PM
please take each scenario and explain to me step by step how worked it out. also show me which parts of the course document you used

09:42 PM
I'll go through each scenario step by step and show you exactly how I worked them out using the course document.

Scenario 1: Find Largest Positive Number
How I worked it out:

Identified the problem requirements:
Ask user to enter positive numbers repeatedly
Stop when user enters zero
Find and output the largest number
Applied the course principles:
From the document: "an algorithm is a step-by-step method of solving a problem"
Used the sandwich example logic: break it into clear steps
Step-by-step breakdown:
Course document references I used:

Example 4 (page 7): "while counter is less than or equal to ten" - showed me the while loop format
Variables section (page 5): "variables act as a kind of 'storage location' for data" - used this for storing 'largest'
Input/Output section (page 4): "Input can be data or information sent to the computer using an input device" - for getting user input
Scenario 2: Greeting with Name
How I worked it out:

Identified the requirements:
Get user's name
Store in variable called "first_name"
Print name with "Hello, World"
Used the exact example from the course:
Course document reference:

Example 2 (page 7): "request user's name, if the input name is equal to 'John' print 'Hello, World'" - I adapted this but removed the condition since we want to greet everyone
Variables section (page 5): The course specifically mentions storing in a variable called "first_name"
My solution:
Scenario 3: Calculate Arithmetic Average
How I worked it out:

Identified what "arithmetic average" means:
Add up all numbers
Divide by how many numbers there are
Need to handle "arbitrary number" of integers
Used the class average example as a template:
Course document reference:

Example 4 (page 7): "set the total class grade to zero, set counter to one, while counter is less than or equal to ten, input the student's grade, add the student's grade to the total class grade, calculate the class average by dividing the total grade sum by ten"
Adapted it for arbitrary numbers:
Scenario 4: Grocery List in Alphabetical Order
How I worked it out:

Identified the requirements:
Read a grocery list
Sort products alphabetically
Print the sorted products
Applied the simple step-by-step approach:
Course document references:

Algorithm definition (page 3): "a step-by-step method of solving a problem"
Clarity principle (page 5): "Your algorithm should be unambiguous"
Kept it simple since the course doesn't cover sorting details:
Why I kept sorting simple: The course document shows that pseudocode should be "easy to write and understand, even if you have no programming experience" (page 6), so I didn't break down the sorting algorithm into detailed steps.

Key Course Principles I Applied Throughout:
Simple Language (page 6): "You simply need to write down a logical breakdown of what you want your program to do"
Natural Language Style (page 7): All examples use phrases like "get grade", "request user's name", "input the student's grade"
Step-by-step Logic (page 3): "an algorithm is a step-by-step method of solving a problem"
Input/Output Focus (page 4): Each algorithm has clear input and output steps
No Strict Syntax (page 6): "Pseudocode does not need to obey any specific syntax rules"
The course emphasizes keeping pseudocode simple and readable, which is why I avoided complex programming constructs and stuck to natural language descriptions that anyone could understand.

1 file changed
+39
