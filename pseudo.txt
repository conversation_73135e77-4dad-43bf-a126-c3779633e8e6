PSEUDOCODE FOR AI AND SOFTWARE ENGINEERING COURSE

Scenario 1: Find Largest Positive Number
=========================================
set largest to 0
get number from user
while number is not equal to 0
    if number is greater than largest
        set largest to number
    get number from user
print largest

Scenario 2: Greeting with Name
==============================
request user's name
store input in variable called first_name
print first_name along with "Hello, World"

Scenario 3: Calculate Arithmetic Average
========================================
set total to 0
set count to 0
get number from user
while user wants to enter more numbers
    add number to total
    add 1 to count
    get number from user
calculate average by dividing total by count
print average

Scenario 4: Grocery List in Alphabetical Order
==============================================
get grocery list from user
sort the grocery list in alphabetical order
print each product in the sorted list

Scenario 5: Algorithm for Flowchart
===================================
read X and Y from user
calculate S by adding X and Y
calculate A by dividing S by 2
calculate P by multiplying X and Y
write S, A, and P
