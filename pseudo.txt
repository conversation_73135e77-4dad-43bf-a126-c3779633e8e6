PSEUDOCODE FOR AI AND SOFTWARE ENGINEERING COURSE

Scenario 1: Find Largest Positive Number Algorithm
=================================================

BEGIN
    SET largest = 0
    SET number = 1

    WHILE number is not equal to 0
        PRINT "Enter a positive number (0 to stop): "
        INPUT number

        IF number > largest THEN
            SET largest = number
        END IF
    END WHILE

    PRINT "The largest number was: " + largest
END
